{"name": "red", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@types/leaflet": "^1.9.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "geist": "^1.4.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "leaflet": "^1.9.4", "leaflet.markercluster": "^1.5.3", "lucide-react": "^0.539.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0", "tailwind-merge": "^3.3.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.7", "typescript": "^5"}}