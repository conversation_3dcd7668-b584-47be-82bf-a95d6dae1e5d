import jsPDF from 'jspdf'
import autoTable from 'jspdf-autotable'
import * as XLSX from 'xlsx'

type Organization = {
  id: number
  name: string
  category: string
  location: string
  address: string
  employees: string
  website: string
  phone: string
  email: string
  twitter: string
  linkedin: string
  description: string
  organizationType: string[]
  founded: number
}

export const exportToPDF = (organizations: Organization[], filters?: {
  searchTerm?: string
  category?: string
  employeeSize?: string
}) => {
  const doc = new jsPDF()
  
  // Add title
  doc.setFontSize(20)
  doc.text('Biogrofe - Biotechnology Organizations', 20, 20)
  
  // Add filters info if any
  let yPosition = 35
  if (filters) {
    doc.setFontSize(12)
    if (filters.searchTerm) {
      doc.text(`Search: ${filters.searchTerm}`, 20, yPosition)
      yPosition += 7
    }
    if (filters.category && filters.category !== 'All Categories') {
      doc.text(`Category: ${filters.category}`, 20, yPosition)
      yPosition += 7
    }
    if (filters.employeeSize && filters.employeeSize !== 'All Sizes') {
      doc.text(`Company Size: ${filters.employeeSize}`, 20, yPosition)
      yPosition += 7
    }
    yPosition += 5
  }
  
  // Add summary
  doc.setFontSize(12)
  doc.text(`Total Organizations: ${organizations.length}`, 20, yPosition)
  doc.text(`Generated: ${new Date().toLocaleDateString()}`, 20, yPosition + 7)
  
  // Prepare table data
  const tableData = organizations.map(org => [
    org.name,
    org.category,
    org.location,
    org.employees,
    org.website,
    org.email,
    org.organizationType.join(', ')
  ])
  
  // Add table
  autoTable(doc, {
    head: [['Name', 'Category', 'Location', 'Size', 'Website', 'Email', 'Type']],
    body: tableData,
    startY: yPosition + 20,
    styles: { fontSize: 8 },
    headStyles: { fillColor: [59, 130, 246] },
    columnStyles: {
      0: { cellWidth: 30 }, // Name
      1: { cellWidth: 25 }, // Category
      2: { cellWidth: 25 }, // Location
      3: { cellWidth: 15 }, // Size
      4: { cellWidth: 30 }, // Website
      5: { cellWidth: 30 }, // Email
      6: { cellWidth: 25 }  // Type
    },
    margin: { left: 10, right: 10 }
  })
  
  // Save the PDF
  const fileName = `biogrofe-organizations-${new Date().toISOString().split('T')[0]}.pdf`
  doc.save(fileName)
}

export const exportToExcel = (organizations: Organization[], filters?: {
  searchTerm?: string
  category?: string
  employeeSize?: string
}) => {
  // Prepare data for Excel
  const excelData = organizations.map(org => ({
    'Organization Name': org.name,
    'Category': org.category,
    'Location': org.location,
    'Address': org.address,
    'Company Size': org.employees,
    'Website': org.website,
    'Phone': org.phone,
    'Email': org.email,
    'Twitter': org.twitter,
    'LinkedIn': org.linkedin,
    'Description': org.description,
    'Organization Type': org.organizationType.join(', '),
    'Founded': org.founded
  }))
  
  // Create workbook and worksheet
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.json_to_sheet(excelData)
  
  // Set column widths
  const colWidths = [
    { wch: 30 }, // Organization Name
    { wch: 20 }, // Category
    { wch: 20 }, // Location
    { wch: 30 }, // Address
    { wch: 15 }, // Company Size
    { wch: 25 }, // Website
    { wch: 15 }, // Phone
    { wch: 25 }, // Email
    { wch: 15 }, // Twitter
    { wch: 20 }, // LinkedIn
    { wch: 50 }, // Description
    { wch: 25 }, // Organization Type
    { wch: 10 }  // Founded
  ]
  ws['!cols'] = colWidths
  
  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(wb, ws, 'Organizations')
  
  // Add a summary sheet if filters are applied
  if (filters) {
    const summaryData = [
      ['Export Summary', ''],
      ['Generated Date', new Date().toLocaleDateString()],
      ['Total Organizations', organizations.length],
      ['', ''],
      ['Applied Filters', ''],
      ['Search Term', filters.searchTerm || 'None'],
      ['Category', filters.category === 'All Categories' ? 'None' : (filters.category || 'None')],
      ['Company Size', filters.employeeSize === 'All Sizes' ? 'None' : (filters.employeeSize || 'None')]
    ]
    
    const summaryWs = XLSX.utils.aoa_to_sheet(summaryData)
    summaryWs['!cols'] = [{ wch: 20 }, { wch: 30 }]
    XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary')
  }
  
  // Save the file
  const fileName = `biogrofe-organizations-${new Date().toISOString().split('T')[0]}.xlsx`
  XLSX.writeFile(wb, fileName)
}

export const exportToCSV = (organizations: Organization[]) => {
  const csvData = organizations.map(org => ({
    'Organization Name': org.name,
    'Category': org.category,
    'Location': org.location,
    'Address': org.address,
    'Company Size': org.employees,
    'Website': org.website,
    'Phone': org.phone,
    'Email': org.email,
    'Twitter': org.twitter,
    'LinkedIn': org.linkedin,
    'Description': org.description,
    'Organization Type': org.organizationType.join(', '),
    'Founded': org.founded
  }))
  
  const ws = XLSX.utils.json_to_sheet(csvData)
  const csv = XLSX.utils.sheet_to_csv(ws)
  
  // Create and download CSV
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `biogrofe-organizations-${new Date().toISOString().split('T')[0]}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
